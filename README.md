# Heartbeat Kernel

This repository contains the `heartbeat.kernel` contract—a simple on-chain health check for KRNL that returns a timestamp and an "OK" status. Use this guide to implement, deploy, and register your kernel on the KRNL testnet.

## Table of Contents

* [Overview](#overview)
* [Prerequisites](#prerequisites)
* [Project Structure](#project-structure)
* [Installation & Setup](#installation--setup)
* [Implementation](#implementation)
* [Testing Locally](#testing-locally)
* [Deployment](#deployment)
* [Registration on Zealy](#registration-on-zealy)
* [Usage Example](#usage-example)
* [Contributing](#contributing)
* [License](#license)

## Overview

The `heartbeat.kernel` is designed as a minimal, dependency-free contract that developers can use to verify the health of the KRNL network. Upon execution, it returns a JSON object with two fields:

* `status`: Always returns "OK" to indicate that the contract is reachable and functioning.
* `ts` (timestamp): The current `block.timestamp` at the moment of execution.

This kernel serves as a foundation for more complex kernels and demonstrates best practices for building and registering a KRNL kernel.

## Prerequisites

Ensure you have the following installed:

* [Node.js (v16+)](https://nodejs.org/)
* [npm or yarn](https://npmjs.com/)
* [Hardhat](https://hardhat.org/) or other preferred Solidity development framework
* KRNL CLI or SDK installed globally (if available)
* A funded wallet configured to connect to the KRNL Testnet
* Access to the KRNL Zealy Quest platform: [https://zealy.io/cw/krnlklub/invite/{YOUR\_INVITE\_CODE}](https://zealy.io/cw/krnlklub/invite/{YOUR_INVITE_CODE})

## Project Structure

```text
heartbeat-kernel/
├── contracts/
│   └── HeartbeatKernel.sol    # Solidity implementation
├── hardhat.config.js         # Hardhat configuration (or equivalent)
├── scripts/
│   └── deploy.js             # Deployment script
├── test/
│   └── heartbeat.test.js     # Unit tests for the kernel
├── README.md                 # This file
└── package.json              # Project dependencies and scripts
```

## Installation & Setup

1. **Clone the repository**

```bash
git clone https://github.com/your-username/heartbeat-kernel.git
cd heartbeat-kernel
```

2. **Install dependencies**

```bash
npm install
# or
yarn install
```

3. **Configure environment**

Create a `.env` file in the root directory and populate with:

```env
PRIVATE_KEY=your-wallet-private-key
RPC_URL=https://testnet.krnl.xyz
```

4. **Compile contracts**

```bash
npx hardhat compile
```

## Implementation

Below is the core of the `HeartbeatKernel.sol` contract, based on KRNL’s kernel boilerplate:

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@krnl/sdk/contracts/KernelBase.sol";

contract HeartbeatKernel is KernelBase {
    constructor() KernelBase("heartbeat.kernel", "Simple health check kernel") {}

    function execute(bytes calldata) external view override returns (bytes memory) {
        // Return a JSON-encoded object: { status: "OK", ts: block.timestamp }
        return abi.encodePacked(
            "{\"status\":\"OK\",\"ts\":",
            uint2str(block.timestamp),
            "}"
        );
    }

    // Utility to convert uint to string
    function uint2str(uint256 _i) internal pure returns (string memory str) {
        if (_i == 0) {
            return "0";
        }
        uint256 j = _i;
        uint256 length;
        while (j != 0) {
            length++;
            j /= 10;
        }
        bytes memory bstr = new bytes(length);
        uint256 k = length - 1;
        while (_i != 0) {
            bstr[k--] = bytes1(uint8(48 + _i % 10));
            _i /= 10;
        }
        str = string(bstr);
    }
}
```

## Testing Locally

Write a simple unit test to validate the response:

```javascript
const { expect } = require("chai");

describe("HeartbeatKernel", function () {
  it("should return status OK and a valid timestamp", async function () {
    const [deployer] = await ethers.getSigners();
    const Heartbeat = await ethers.getContractFactory("HeartbeatKernel");
    const heartbeat = await Heartbeat.deploy();

    const resultBytes = await heartbeat.execute([]);
    const resultString = ethers.utils.toUtf8String(resultBytes);
    const parsed = JSON.parse(resultString);

    expect(parsed.status).to.equal("OK");
    expect(parsed.ts).to.be.a("number");
  });
});
```

Run tests with:

```bash
npx hardhat test
```

## Deployment

Use the provided deployment script to deploy on KRNL Testnet:

```javascript
// scripts/deploy.js
async function main() {
  const Heartbeat = await ethers.getContractFactory("HeartbeatKernel");
  const heartbeat = await Heartbeat.deploy();
  await heartbeat.deployed();
  console.log("HeartbeatKernel deployed to:", heartbeat.address);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
```

Deploy:

```bash
npx hardhat run scripts/deploy.js --network krnl-testnet
```

Make note of the deployed contract address.

## Registration on Zealy

1. Navigate to the KRNL Zealy page: `https://zealy.io/cw/krnlklub/invite/{YOUR_INVITE_CODE}`
2. Find the **Register a unique kernel** quest.
3. Click **Submit**, and provide:

   * **Name:** `heartbeat.kernel`
   * **Address:** The address from your deployment logs
   * **Description:** "A simple on-chain health check kernel that returns status OK and the current block timestamp."
4. Submit and await confirmation. Upon acceptance, you’ll earn **50 XP**.

## Usage Example

Once registered, other developers can execute your kernel via KRNL SDK:

```javascript
import { KRNLClient } from "@krnl/sdk";

async function checkHealth() {
  const client = new KRNLClient({ rpcUrl: "https://testnet.krnl.xyz" });
  const response = await client.executeKernel("heartbeat.kernel", []);
  console.log("Health Check:", response);
}
```

## Contributing

Contributions are welcome! Feel free to open issues or submit pull requests for:

* Performance optimizations
* Extended health metrics
* Improved documentation or examples

## License

This project is licensed under the MIT License. See [LICENSE](LICENSE) for details.
