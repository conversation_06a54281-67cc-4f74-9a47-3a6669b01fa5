{"id": "7ab539105f69b9459e21be35b4b6a4c2", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.19", "solcLongVersion": "0.8.19+commit.7dd6d404", "input": {"language": "Solidity", "sources": {"contracts/HeartbeatKernel.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.19;\n\n/**\n * @title HeartbeatKernel\n * @dev A minimal kernel that returns health status and current block timestamp for on-chain monitoring\n * @notice This contract provides a simple heartbeat function that can be registered as a KRNL kernel\n */\ncontract HeartbeatKernel {\n    \n    // Events for monitoring\n    event HeartbeatChecked(address indexed caller, uint256 timestamp, string status);\n    \n    // Contract metadata\n    string public constant name = \"heartbeat.kernel\";\n    string public constant version = \"1.0.0\";\n    string public constant description = \"A minimal kernel that returns health status and current block timestamp for on-chain monitoring\";\n    \n    // Deployment timestamp for uptime calculation\n    uint256 public immutable deployedAt;\n    \n    constructor() {\n        deployedAt = block.timestamp;\n    }\n    \n    /**\n     * @dev Main heartbeat function that returns status and timestamp\n     * @return status Always returns \"OK\" indicating the contract is operational\n     * @return timestamp Current block timestamp\n     */\n    function execute() external view returns (string memory status, uint256 timestamp) {\n        return (\"OK\", block.timestamp);\n    }\n    \n    /**\n     * @dev Extended heartbeat function with additional information\n     * @return status Always returns \"OK\" indicating the contract is operational\n     * @return timestamp Current block timestamp\n     * @return blockNumber Current block number\n     * @return uptime Time elapsed since contract deployment\n     */\n    function executeExtended() external view returns (\n        string memory status, \n        uint256 timestamp, \n        uint256 blockNumber, \n        uint256 uptime\n    ) {\n        return (\n            \"OK\", \n            block.timestamp, \n            block.number, \n            block.timestamp - deployedAt\n        );\n    }\n    \n    /**\n     * @dev Get contract metadata\n     * @return contractName The name of the kernel\n     * @return contractVersion The version of the kernel\n     * @return contractDescription The description of the kernel\n     */\n    function getMetadata() external pure returns (\n        string memory contractName,\n        string memory contractVersion,\n        string memory contractDescription\n    ) {\n        return (name, version, description);\n    }\n    \n    /**\n     * @dev Check if the contract is healthy (always returns true for this simple implementation)\n     * @return healthy Always returns true\n     */\n    function isHealthy() external pure returns (bool healthy) {\n        return true;\n    }\n    \n    /**\n     * @dev Get deployment information\n     * @return deploymentTimestamp When the contract was deployed\n     * @return currentUptime Current uptime in seconds\n     */\n    function getDeploymentInfo() external view returns (\n        uint256 deploymentTimestamp,\n        uint256 currentUptime\n    ) {\n        return (deployedAt, block.timestamp - deployedAt);\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"contracts/HeartbeatKernel.sol": {"ast": {"absolutePath": "contracts/HeartbeatKernel.sol", "exportedSymbols": {"HeartbeatKernel": [110]}, "id": 111, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".19"], "nodeType": "PragmaDirective", "src": "32:24:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "HeartbeatKernel", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 2, "nodeType": "StructuredDocumentation", "src": "58:239:0", "text": " @title HeartbeatKernel\n @dev A minimal kernel that returns health status and current block timestamp for on-chain monitoring\n @notice This contract provides a simple heartbeat function that can be registered as a KRNL kernel"}, "fullyImplemented": true, "id": 110, "linearizedBaseContracts": [110], "name": "HeartbeatKernel", "nameLocation": "307:15:0", "nodeType": "ContractDefinition", "nodes": [{"anonymous": false, "eventSelector": "a3b47f1b4bc17e89284dc8b715358287846f7d82c804758e015c99e4c0bdea23", "id": 10, "name": "HeartbeatChecked", "nameLocation": "369:16:0", "nodeType": "EventDefinition", "parameters": {"id": 9, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4, "indexed": true, "mutability": "mutable", "name": "caller", "nameLocation": "402:6:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "386:22:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3, "name": "address", "nodeType": "ElementaryTypeName", "src": "386:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 6, "indexed": false, "mutability": "mutable", "name": "timestamp", "nameLocation": "418:9:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "410:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 5, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "410:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 8, "indexed": false, "mutability": "mutable", "name": "status", "nameLocation": "436:6:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "429:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 7, "name": "string", "nodeType": "ElementaryTypeName", "src": "429:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "385:58:0"}, "src": "363:81:0"}, {"constant": true, "functionSelector": "06fdde03", "id": 13, "mutability": "constant", "name": "name", "nameLocation": "502:4:0", "nodeType": "VariableDeclaration", "scope": 110, "src": "479:48:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 11, "name": "string", "nodeType": "ElementaryTypeName", "src": "479:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "6865617274626561742e6b65726e656c", "id": 12, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "509:18:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_bd6c224da71e57b309bf9a08e324d73bfc28317887452da67945439906caf793", "typeString": "literal_string \"heartbeat.kernel\""}, "value": "heartbeat.kernel"}, "visibility": "public"}, {"constant": true, "functionSelector": "54fd4d50", "id": 16, "mutability": "constant", "name": "version", "nameLocation": "556:7:0", "nodeType": "VariableDeclaration", "scope": 110, "src": "533:40:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 14, "name": "string", "nodeType": "ElementaryTypeName", "src": "533:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "312e302e30", "id": 15, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "566:7:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_06c015bd22b4c69690933c1058878ebdfef31f9aaae40bbe86d8a09fe1b2972c", "typeString": "literal_string \"1.0.0\""}, "value": "1.0.0"}, "visibility": "public"}, {"constant": true, "functionSelector": "7284e416", "id": 19, "mutability": "constant", "name": "description", "nameLocation": "602:11:0", "nodeType": "VariableDeclaration", "scope": 110, "src": "579:134:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 17, "name": "string", "nodeType": "ElementaryTypeName", "src": "579:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "41206d696e696d616c206b65726e656c20746861742072657475726e73206865616c74682073746174757320616e642063757272656e7420626c6f636b2074696d657374616d7020666f72206f6e2d636861696e206d6f6e69746f72696e67", "id": 18, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "616:97:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d2b34fe0a34a74b8a9527f67320a6aff694c5a8136ceae9c3de4a975b28784ed", "typeString": "literal_string \"A minimal kernel that returns health status and current block timestamp for on-chain monitoring\""}, "value": "A minimal kernel that returns health status and current block timestamp for on-chain monitoring"}, "visibility": "public"}, {"constant": false, "functionSelector": "eae4c19f", "id": 21, "mutability": "immutable", "name": "deployedAt", "nameLocation": "800:10:0", "nodeType": "VariableDeclaration", "scope": 110, "src": "775:35:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 20, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "775:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"body": {"id": 29, "nodeType": "Block", "src": "835:45:0", "statements": [{"expression": {"id": 27, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 24, "name": "deployedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "845:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 25, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "858:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 26, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "864:9:0", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "858:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "845:28:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 28, "nodeType": "ExpressionStatement", "src": "845:28:0"}]}, "id": 30, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 22, "nodeType": "ParameterList", "parameters": [], "src": "832:2:0"}, "returnParameters": {"id": 23, "nodeType": "ParameterList", "parameters": [], "src": "835:0:0"}, "scope": 110, "src": "821:59:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 43, "nodeType": "Block", "src": "1189:47:0", "statements": [{"expression": {"components": [{"hexValue": "4f4b", "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1207:4:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_daa4f5d475c02455977059e29f0bd4c2f7e92e8cfa8026b3dcd2c374e8a520e6", "typeString": "literal_string \"OK\""}, "value": "OK"}, {"expression": {"id": 39, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "1213:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 40, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1219:9:0", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "1213:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 41, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1206:23:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_stringliteral_daa4f5d475c02455977059e29f0bd4c2f7e92e8cfa8026b3dcd2c374e8a520e6_$_t_uint256_$", "typeString": "tuple(literal_string \"OK\",uint256)"}}, "functionReturnParameters": 37, "id": 42, "nodeType": "Return", "src": "1199:30:0"}]}, "documentation": {"id": 31, "nodeType": "StructuredDocumentation", "src": "890:211:0", "text": " @dev Main heartbeat function that returns status and timestamp\n @return status Always returns \"OK\" indicating the contract is operational\n @return timestamp Current block timestamp"}, "functionSelector": "61461954", "id": 44, "implemented": true, "kind": "function", "modifiers": [], "name": "execute", "nameLocation": "1115:7:0", "nodeType": "FunctionDefinition", "parameters": {"id": 32, "nodeType": "ParameterList", "parameters": [], "src": "1122:2:0"}, "returnParameters": {"id": 37, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 34, "mutability": "mutable", "name": "status", "nameLocation": "1162:6:0", "nodeType": "VariableDeclaration", "scope": 44, "src": "1148:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 33, "name": "string", "nodeType": "ElementaryTypeName", "src": "1148:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 36, "mutability": "mutable", "name": "timestamp", "nameLocation": "1178:9:0", "nodeType": "VariableDeclaration", "scope": 44, "src": "1170:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 35, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1170:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1147:41:0"}, "scope": 110, "src": "1106:130:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 67, "nodeType": "Block", "src": "1738:152:0", "statements": [{"expression": {"components": [{"hexValue": "4f4b", "id": 56, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1769:4:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_daa4f5d475c02455977059e29f0bd4c2f7e92e8cfa8026b3dcd2c374e8a520e6", "typeString": "literal_string \"OK\""}, "value": "OK"}, {"expression": {"id": 57, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "1788:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 58, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1794:9:0", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "1788:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 59, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "1818:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 60, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1824:6:0", "memberName": "number", "nodeType": "MemberAccess", "src": "1818:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 64, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 61, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "1845:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 62, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1851:9:0", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "1845:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 63, "name": "deployedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "1863:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1845:28:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 65, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1755:128:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_stringliteral_daa4f5d475c02455977059e29f0bd4c2f7e92e8cfa8026b3dcd2c374e8a520e6_$_t_uint256_$_t_uint256_$_t_uint256_$", "typeString": "tuple(literal_string \"OK\",uint256,uint256,uint256)"}}, "functionReturnParameters": 55, "id": 66, "nodeType": "Return", "src": "1748:135:0"}]}, "documentation": {"id": 45, "nodeType": "StructuredDocumentation", "src": "1246:318:0", "text": " @dev Extended heartbeat function with additional information\n @return status Always returns \"OK\" indicating the contract is operational\n @return timestamp Current block timestamp\n @return blockNumber Current block number\n @return uptime Time elapsed since contract deployment"}, "functionSelector": "dabcb7da", "id": 68, "implemented": true, "kind": "function", "modifiers": [], "name": "executeExtended", "nameLocation": "1578:15:0", "nodeType": "FunctionDefinition", "parameters": {"id": 46, "nodeType": "ParameterList", "parameters": [], "src": "1593:2:0"}, "returnParameters": {"id": 55, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 48, "mutability": "mutable", "name": "status", "nameLocation": "1642:6:0", "nodeType": "VariableDeclaration", "scope": 68, "src": "1628:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 47, "name": "string", "nodeType": "ElementaryTypeName", "src": "1628:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 50, "mutability": "mutable", "name": "timestamp", "nameLocation": "1667:9:0", "nodeType": "VariableDeclaration", "scope": 68, "src": "1659:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 49, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1659:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 52, "mutability": "mutable", "name": "blockNumber", "nameLocation": "1695:11:0", "nodeType": "VariableDeclaration", "scope": 68, "src": "1687:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 51, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1687:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 54, "mutability": "mutable", "name": "uptime", "nameLocation": "1725:6:0", "nodeType": "VariableDeclaration", "scope": 68, "src": "1717:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 53, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1717:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1618:119:0"}, "scope": 110, "src": "1569:321:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 83, "nodeType": "Block", "src": "2293:52:0", "statements": [{"expression": {"components": [{"id": 78, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "2311:4:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 79, "name": "version", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 16, "src": "2317:7:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 80, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "2326:11:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "id": 81, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2310:28:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_string_memory_ptr_$", "typeString": "tuple(string memory,string memory,string memory)"}}, "functionReturnParameters": 77, "id": 82, "nodeType": "Return", "src": "2303:35:0"}]}, "documentation": {"id": 69, "nodeType": "StructuredDocumentation", "src": "1900:218:0", "text": " @dev Get contract metadata\n @return contractName The name of the kernel\n @return contractVersion The version of the kernel\n @return contractDescription The description of the kernel"}, "functionSelector": "7a5b4f59", "id": 84, "implemented": true, "kind": "function", "modifiers": [], "name": "getMetadata", "nameLocation": "2132:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 70, "nodeType": "ParameterList", "parameters": [], "src": "2143:2:0"}, "returnParameters": {"id": 77, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 72, "mutability": "mutable", "name": "contractName", "nameLocation": "2192:12:0", "nodeType": "VariableDeclaration", "scope": 84, "src": "2178:26:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 71, "name": "string", "nodeType": "ElementaryTypeName", "src": "2178:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 74, "mutability": "mutable", "name": "contractVersion", "nameLocation": "2228:15:0", "nodeType": "VariableDeclaration", "scope": 84, "src": "2214:29:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 73, "name": "string", "nodeType": "ElementaryTypeName", "src": "2214:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 76, "mutability": "mutable", "name": "contractDescription", "nameLocation": "2267:19:0", "nodeType": "VariableDeclaration", "scope": 84, "src": "2253:33:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 75, "name": "string", "nodeType": "ElementaryTypeName", "src": "2253:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2168:124:0"}, "scope": 110, "src": "2123:222:0", "stateMutability": "pure", "virtual": false, "visibility": "external"}, {"body": {"id": 92, "nodeType": "Block", "src": "2570:28:0", "statements": [{"expression": {"hexValue": "74727565", "id": 90, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2587:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 89, "id": 91, "nodeType": "Return", "src": "2580:11:0"}]}, "documentation": {"id": 85, "nodeType": "StructuredDocumentation", "src": "2355:152:0", "text": " @dev Check if the contract is healthy (always returns true for this simple implementation)\n @return healthy Always returns true"}, "functionSelector": "e4020804", "id": 93, "implemented": true, "kind": "function", "modifiers": [], "name": "is<PERSON><PERSON><PERSON>", "nameLocation": "2521:9:0", "nodeType": "FunctionDefinition", "parameters": {"id": 86, "nodeType": "ParameterList", "parameters": [], "src": "2530:2:0"}, "returnParameters": {"id": 89, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 88, "mutability": "mutable", "name": "healthy", "nameLocation": "2561:7:0", "nodeType": "VariableDeclaration", "scope": 93, "src": "2556:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 87, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2556:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2555:14:0"}, "scope": 110, "src": "2512:86:0", "stateMutability": "pure", "virtual": false, "visibility": "external"}, {"body": {"id": 108, "nodeType": "Block", "src": "2910:66:0", "statements": [{"expression": {"components": [{"id": 101, "name": "deployedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "2928:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 105, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 102, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "2940:5:0", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 103, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2946:9:0", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "2940:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 104, "name": "deployedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "2958:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2940:28:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 106, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2927:42:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint256_$_t_uint256_$", "typeString": "tuple(uint256,uint256)"}}, "functionReturnParameters": 100, "id": 107, "nodeType": "Return", "src": "2920:49:0"}]}, "documentation": {"id": 94, "nodeType": "StructuredDocumentation", "src": "2608:171:0", "text": " @dev Get deployment information\n @return deploymentTimestamp When the contract was deployed\n @return currentUptime Current uptime in seconds"}, "functionSelector": "aeefffad", "id": 109, "implemented": true, "kind": "function", "modifiers": [], "name": "getDeploymentInfo", "nameLocation": "2793:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 95, "nodeType": "ParameterList", "parameters": [], "src": "2810:2:0"}, "returnParameters": {"id": 100, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 97, "mutability": "mutable", "name": "deploymentTimestamp", "nameLocation": "2853:19:0", "nodeType": "VariableDeclaration", "scope": 109, "src": "2845:27:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 96, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2845:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 99, "mutability": "mutable", "name": "currentUptime", "nameLocation": "2890:13:0", "nodeType": "VariableDeclaration", "scope": 109, "src": "2882:21:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 98, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2882:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2835:74:0"}, "scope": 110, "src": "2784:192:0", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 111, "src": "298:2680:0", "usedErrors": []}], "src": "32:2947:0"}, "id": 0}}, "contracts": {"contracts/HeartbeatKernel.sol": {"HeartbeatKernel": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "status", "type": "string"}], "name": "HeartbeatChecked", "type": "event"}, {"inputs": [], "name": "deployedAt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "description", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "execute", "outputs": [{"internalType": "string", "name": "status", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "executeExtended", "outputs": [{"internalType": "string", "name": "status", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "uint256", "name": "uptime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDeploymentInfo", "outputs": [{"internalType": "uint256", "name": "deploymentTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "currentUptime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMetadata", "outputs": [{"internalType": "string", "name": "contractName", "type": "string"}, {"internalType": "string", "name": "contractVersion", "type": "string"}, {"internalType": "string", "name": "contractDescription", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "is<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "healthy", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {"@_30": {"entryPoint": null, "id": 30, "parameterSlots": 0, "returnSlots": 0}}, "generatedSources": [], "linkReferences": {}, "object": "60a060405234801561001057600080fd5b504260805260805161048961003e6000396000818161018f01528181610250015261028e01526104896000f3fe608060405234801561001057600080fd5b50600436106100935760003560e01c80637a5b4f59116100665780637a5b4f591461012f578063aeefffad14610146578063dabcb7da14610163578063e40208041461017b578063eae4c19f1461018a57600080fd5b806306fdde031461009857806354fd4d50146100dd57806361461954146101015780637284e41614610127575b600080fd5b6100c76040518060400160405280601081526020016f1a19585c9d1899585d0b9ad95c9b995b60821b81525081565b6040516100d4919061031f565b60405180910390f35b6100c7604051806040016040528060058152602001640312e302e360dc1b81525081565b60408051808201825260028152614f4b60f01b602082015290516100d491904290610339565b6100c76101bf565b6101376101db565b6040516100d49392919061035b565b61014e61024b565b604080519283526020830191909152016100d4565b61016b610281565b6040516100d4949392919061039e565b604051600181526020016100d4565b6101b17f000000000000000000000000000000000000000000000000000000000000000081565b6040519081526020016100d4565b6040518060800160405280605f81526020016103f5605f913981565b60608060606040518060400160405280601081526020016f1a19585c9d1899585d0b9ad95c9b995b60821b815250604051806040016040528060058152602001640312e302e360dc1b8152506040518060800160405280605f81526020016103f5605f9139925092509250909192565b6000807f000000000000000000000000000000000000000000000000000000000000000061027981426103cd565b915091509091565b60606000808042436102b37f0000000000000000000000000000000000000000000000000000000000000000836103cd565b6040805180820190915260028152614f4b60f01b60208201529792965090945092509050565b6000815180845260005b818110156102ff576020818501810151868301820152016102e3565b506000602082860101526020601f19601f83011685010191505092915050565b60208152600061033260208301846102d9565b9392505050565b60408152600061034c60408301856102d9565b90508260208301529392505050565b60608152600061036e60608301866102d9565b828103602084015261038081866102d9565b9050828103604084015261039481856102d9565b9695505050505050565b6080815260006103b160808301876102d9565b6020830195909552506040810192909252606090910152919050565b818103818111156103ee57634e487b7160e01b600052601160045260246000fd5b9291505056fe41206d696e696d616c206b65726e656c20746861742072657475726e73206865616c74682073746174757320616e642063757272656e7420626c6f636b2074696d657374616d7020666f72206f6e2d636861696e206d6f6e69746f72696e67a26469706673582212200d5c90eb0f4175cc7f968a98a871940184ccfcdabb978f386c34470a5f11727c64736f6c63430008130033", "opcodes": "PUSH1 0xA0 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP TIMESTAMP PUSH1 0x80 MSTORE PUSH1 0x80 MLOAD PUSH2 0x489 PUSH2 0x3E PUSH1 0x0 CODECOPY PUSH1 0x0 DUP2 DUP2 PUSH2 0x18F ADD MSTORE DUP2 DUP2 PUSH2 0x250 ADD MSTORE PUSH2 0x28E ADD MSTORE PUSH2 0x489 PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x93 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x7A5B4F59 GT PUSH2 0x66 JUMPI DUP1 PUSH4 0x7A5B4F59 EQ PUSH2 0x12F JUMPI DUP1 PUSH4 0xAEEFFFAD EQ PUSH2 0x146 JUMPI DUP1 PUSH4 0xDABCB7DA EQ PUSH2 0x163 JUMPI DUP1 PUSH4 0xE4020804 EQ PUSH2 0x17B JUMPI DUP1 PUSH4 0xEAE4C19F EQ PUSH2 0x18A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x6FDDE03 EQ PUSH2 0x98 JUMPI DUP1 PUSH4 0x54FD4D50 EQ PUSH2 0xDD JUMPI DUP1 PUSH4 0x61461954 EQ PUSH2 0x101 JUMPI DUP1 PUSH4 0x7284E416 EQ PUSH2 0x127 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0xC7 PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x10 DUP2 MSTORE PUSH1 0x20 ADD PUSH16 0x1A19585C9D1899585D0B9AD95C9B995B PUSH1 0x82 SHL DUP2 MSTORE POP DUP2 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xD4 SWAP2 SWAP1 PUSH2 0x31F JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0xC7 PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5 DUP2 MSTORE PUSH1 0x20 ADD PUSH5 0x312E302E3 PUSH1 0xDC SHL DUP2 MSTORE POP DUP2 JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD DUP1 DUP3 ADD DUP3 MSTORE PUSH1 0x2 DUP2 MSTORE PUSH2 0x4F4B PUSH1 0xF0 SHL PUSH1 0x20 DUP3 ADD MSTORE SWAP1 MLOAD PUSH2 0xD4 SWAP2 SWAP1 TIMESTAMP SWAP1 PUSH2 0x339 JUMP JUMPDEST PUSH2 0xC7 PUSH2 0x1BF JUMP JUMPDEST PUSH2 0x137 PUSH2 0x1DB JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xD4 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x35B JUMP JUMPDEST PUSH2 0x14E PUSH2 0x24B JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD SWAP3 DUP4 MSTORE PUSH1 0x20 DUP4 ADD SWAP2 SWAP1 SWAP2 MSTORE ADD PUSH2 0xD4 JUMP JUMPDEST PUSH2 0x16B PUSH2 0x281 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xD4 SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x39E JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x1 DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0xD4 JUMP JUMPDEST PUSH2 0x1B1 PUSH32 0x0 DUP2 JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0xD4 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0x80 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5F DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x3F5 PUSH1 0x5F SWAP2 CODECOPY DUP2 JUMP JUMPDEST PUSH1 0x60 DUP1 PUSH1 0x60 PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x10 DUP2 MSTORE PUSH1 0x20 ADD PUSH16 0x1A19585C9D1899585D0B9AD95C9B995B PUSH1 0x82 SHL DUP2 MSTORE POP PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5 DUP2 MSTORE PUSH1 0x20 ADD PUSH5 0x312E302E3 PUSH1 0xDC SHL DUP2 MSTORE POP PUSH1 0x40 MLOAD DUP1 PUSH1 0x80 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5F DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x3F5 PUSH1 0x5F SWAP2 CODECOPY SWAP3 POP SWAP3 POP SWAP3 POP SWAP1 SWAP2 SWAP3 JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH32 0x0 PUSH2 0x279 DUP2 TIMESTAMP PUSH2 0x3CD JUMP JUMPDEST SWAP2 POP SWAP2 POP SWAP1 SWAP2 JUMP JUMPDEST PUSH1 0x60 PUSH1 0x0 DUP1 DUP1 TIMESTAMP NUMBER PUSH2 0x2B3 PUSH32 0x0 DUP4 PUSH2 0x3CD JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD DUP1 DUP3 ADD SWAP1 SWAP2 MSTORE PUSH1 0x2 DUP2 MSTORE PUSH2 0x4F4B PUSH1 0xF0 SHL PUSH1 0x20 DUP3 ADD MSTORE SWAP8 SWAP3 SWAP7 POP SWAP1 SWAP5 POP SWAP3 POP SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD DUP1 DUP5 MSTORE PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x2FF JUMPI PUSH1 0x20 DUP2 DUP6 ADD DUP2 ADD MLOAD DUP7 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0x2E3 JUMP JUMPDEST POP PUSH1 0x0 PUSH1 0x20 DUP3 DUP7 ADD ADD MSTORE PUSH1 0x20 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND DUP6 ADD ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x20 DUP2 MSTORE PUSH1 0x0 PUSH2 0x332 PUSH1 0x20 DUP4 ADD DUP5 PUSH2 0x2D9 JUMP JUMPDEST SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x40 DUP2 MSTORE PUSH1 0x0 PUSH2 0x34C PUSH1 0x40 DUP4 ADD DUP6 PUSH2 0x2D9 JUMP JUMPDEST SWAP1 POP DUP3 PUSH1 0x20 DUP4 ADD MSTORE SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x60 DUP2 MSTORE PUSH1 0x0 PUSH2 0x36E PUSH1 0x60 DUP4 ADD DUP7 PUSH2 0x2D9 JUMP JUMPDEST DUP3 DUP2 SUB PUSH1 0x20 DUP5 ADD MSTORE PUSH2 0x380 DUP2 DUP7 PUSH2 0x2D9 JUMP JUMPDEST SWAP1 POP DUP3 DUP2 SUB PUSH1 0x40 DUP5 ADD MSTORE PUSH2 0x394 DUP2 DUP6 PUSH2 0x2D9 JUMP JUMPDEST SWAP7 SWAP6 POP POP POP POP POP POP JUMP JUMPDEST PUSH1 0x80 DUP2 MSTORE PUSH1 0x0 PUSH2 0x3B1 PUSH1 0x80 DUP4 ADD DUP8 PUSH2 0x2D9 JUMP JUMPDEST PUSH1 0x20 DUP4 ADD SWAP6 SWAP1 SWAP6 MSTORE POP PUSH1 0x40 DUP2 ADD SWAP3 SWAP1 SWAP3 MSTORE PUSH1 0x60 SWAP1 SWAP2 ADD MSTORE SWAP2 SWAP1 POP JUMP JUMPDEST DUP2 DUP2 SUB DUP2 DUP2 GT ISZERO PUSH2 0x3EE JUMPI PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID COINBASE KECCAK256 PUSH14 0x696E696D616C206B65726E656C20 PUSH21 0x6861742072657475726E73206865616C7468207374 PUSH2 0x7475 PUSH20 0x20616E642063757272656E7420626C6F636B2074 PUSH10 0x6D657374616D7020666F PUSH19 0x206F6E2D636861696E206D6F6E69746F72696E PUSH8 0xA264697066735822 SLT KECCAK256 0xD 0x5C SWAP1 0xEB 0xF COINBASE PUSH22 0xCC7F968A98A871940184CCFCDABB978F386C34470A5F GT PUSH19 0x7C64736F6C6343000813003300000000000000 ", "sourceMap": "298:2680:0:-:0;;;821:59;;;;;;;;;-1:-1:-1;858:15:0;845:28;;298:2680;;;;;;;;;;;;;;;;;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@deployedAt_21": {"entryPoint": null, "id": 21, "parameterSlots": 0, "returnSlots": 0}, "@description_19": {"entryPoint": 447, "id": 19, "parameterSlots": 0, "returnSlots": 0}, "@executeExtended_68": {"entryPoint": 641, "id": 68, "parameterSlots": 0, "returnSlots": 4}, "@execute_44": {"entryPoint": null, "id": 44, "parameterSlots": 0, "returnSlots": 2}, "@getDeploymentInfo_109": {"entryPoint": 587, "id": 109, "parameterSlots": 0, "returnSlots": 2}, "@getMetadata_84": {"entryPoint": 475, "id": 84, "parameterSlots": 0, "returnSlots": 3}, "@isHealthy_93": {"entryPoint": null, "id": 93, "parameterSlots": 0, "returnSlots": 1}, "@name_13": {"entryPoint": null, "id": 13, "parameterSlots": 0, "returnSlots": 0}, "@version_16": {"entryPoint": null, "id": 16, "parameterSlots": 0, "returnSlots": 0}, "abi_encode_string": {"entryPoint": 729, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": 799, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr_t_string_memory_ptr_t_string_memory_ptr__to_t_string_memory_ptr_t_string_memory_ptr_t_string_memory_ptr__fromStack_reversed": {"entryPoint": 859, "id": null, "parameterSlots": 4, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_uint256__fromStack_reversed": {"entryPoint": 825, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_encode_tuple_t_string_memory_ptr_t_uint256_t_uint256_t_uint256__to_t_string_memory_ptr_t_uint256_t_uint256_t_uint256__fromStack_reversed": {"entryPoint": 926, "id": null, "parameterSlots": 5, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 3, "returnSlots": 1}, "checked_sub_t_uint256": {"entryPoint": 973, "id": null, "parameterSlots": 2, "returnSlots": 1}}, "generatedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:2808:1", "statements": [{"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:1", "statements": []}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "64:373:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "74:26:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "94:5:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "88:5:1"}, "nodeType": "YulFunctionCall", "src": "88:12:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "78:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "116:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "121:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "109:6:1"}, "nodeType": "YulFunctionCall", "src": "109:19:1"}, "nodeType": "YulExpressionStatement", "src": "109:19:1"}, {"nodeType": "YulVariableDeclaration", "src": "137:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "146:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "141:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "208:110:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "222:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "232:4:1", "type": "", "value": "0x20"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "226:2:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "264:3:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "269:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "260:3:1"}, "nodeType": "YulFunctionCall", "src": "260:11:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "273:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "256:3:1"}, "nodeType": "YulFunctionCall", "src": "256:20:1"}, {"arguments": [{"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "292:5:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "299:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "288:3:1"}, "nodeType": "YulFunctionCall", "src": "288:13:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "303:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "284:3:1"}, "nodeType": "YulFunctionCall", "src": "284:22:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "278:5:1"}, "nodeType": "YulFunctionCall", "src": "278:29:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "249:6:1"}, "nodeType": "YulFunctionCall", "src": "249:59:1"}, "nodeType": "YulExpressionStatement", "src": "249:59:1"}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "167:1:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "170:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "164:2:1"}, "nodeType": "YulFunctionCall", "src": "164:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "178:21:1", "statements": [{"nodeType": "YulAssignment", "src": "180:17:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "189:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "192:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "185:3:1"}, "nodeType": "YulFunctionCall", "src": "185:12:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "180:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "160:3:1", "statements": []}, "src": "156:162:1"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "342:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "347:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "338:3:1"}, "nodeType": "YulFunctionCall", "src": "338:16:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "356:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "334:3:1"}, "nodeType": "YulFunctionCall", "src": "334:27:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "363:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "327:6:1"}, "nodeType": "YulFunctionCall", "src": "327:38:1"}, "nodeType": "YulExpressionStatement", "src": "327:38:1"}, {"nodeType": "YulAssignment", "src": "374:57:1", "value": {"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "389:3:1"}, {"arguments": [{"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "402:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "410:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "398:3:1"}, "nodeType": "YulFunctionCall", "src": "398:15:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "419:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "415:3:1"}, "nodeType": "YulFunctionCall", "src": "415:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "394:3:1"}, "nodeType": "YulFunctionCall", "src": "394:29:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "385:3:1"}, "nodeType": "YulFunctionCall", "src": "385:39:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "426:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "381:3:1"}, "nodeType": "YulFunctionCall", "src": "381:50:1"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "374:3:1"}]}]}, "name": "abi_encode_string", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "41:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "48:3:1", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "56:3:1", "type": ""}], "src": "14:423:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "563:99:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "580:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "591:2:1", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "573:6:1"}, "nodeType": "YulFunctionCall", "src": "573:21:1"}, "nodeType": "YulExpressionStatement", "src": "573:21:1"}, {"nodeType": "YulAssignment", "src": "603:53:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "629:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "641:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "652:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "637:3:1"}, "nodeType": "YulFunctionCall", "src": "637:18:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "611:17:1"}, "nodeType": "YulFunctionCall", "src": "611:45:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "603:4:1"}]}]}, "name": "abi_encode_tuple_t_string_memory_ptr__to_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "532:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "543:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "554:4:1", "type": ""}], "src": "442:220:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "816:142:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "833:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "844:2:1", "type": "", "value": "64"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "826:6:1"}, "nodeType": "YulFunctionCall", "src": "826:21:1"}, "nodeType": "YulExpressionStatement", "src": "826:21:1"}, {"nodeType": "YulAssignment", "src": "856:53:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "882:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "894:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "905:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "890:3:1"}, "nodeType": "YulFunctionCall", "src": "890:18:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "864:17:1"}, "nodeType": "YulFunctionCall", "src": "864:45:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "856:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "929:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "940:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "925:3:1"}, "nodeType": "YulFunctionCall", "src": "925:18:1"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "945:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "918:6:1"}, "nodeType": "YulFunctionCall", "src": "918:34:1"}, "nodeType": "YulExpressionStatement", "src": "918:34:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "777:9:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "788:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "796:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "807:4:1", "type": ""}], "src": "667:291:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1180:329:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1197:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1208:2:1", "type": "", "value": "96"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1190:6:1"}, "nodeType": "YulFunctionCall", "src": "1190:21:1"}, "nodeType": "YulExpressionStatement", "src": "1190:21:1"}, {"nodeType": "YulVariableDeclaration", "src": "1220:59:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "1252:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1264:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1275:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1260:3:1"}, "nodeType": "YulFunctionCall", "src": "1260:18:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "1234:17:1"}, "nodeType": "YulFunctionCall", "src": "1234:45:1"}, "variables": [{"name": "tail_1", "nodeType": "YulTypedName", "src": "1224:6:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1299:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1310:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1295:3:1"}, "nodeType": "YulFunctionCall", "src": "1295:18:1"}, {"arguments": [{"name": "tail_1", "nodeType": "YulIdentifier", "src": "1319:6:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "1327:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1315:3:1"}, "nodeType": "YulFunctionCall", "src": "1315:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1288:6:1"}, "nodeType": "YulFunctionCall", "src": "1288:50:1"}, "nodeType": "YulExpressionStatement", "src": "1288:50:1"}, {"nodeType": "YulVariableDeclaration", "src": "1347:47:1", "value": {"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "1379:6:1"}, {"name": "tail_1", "nodeType": "YulIdentifier", "src": "1387:6:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "1361:17:1"}, "nodeType": "YulFunctionCall", "src": "1361:33:1"}, "variables": [{"name": "tail_2", "nodeType": "YulTypedName", "src": "1351:6:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1414:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1425:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1410:3:1"}, "nodeType": "YulFunctionCall", "src": "1410:18:1"}, {"arguments": [{"name": "tail_2", "nodeType": "YulIdentifier", "src": "1434:6:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "1442:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1430:3:1"}, "nodeType": "YulFunctionCall", "src": "1430:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1403:6:1"}, "nodeType": "YulFunctionCall", "src": "1403:50:1"}, "nodeType": "YulExpressionStatement", "src": "1403:50:1"}, {"nodeType": "YulAssignment", "src": "1462:41:1", "value": {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "1488:6:1"}, {"name": "tail_2", "nodeType": "YulIdentifier", "src": "1496:6:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "1470:17:1"}, "nodeType": "YulFunctionCall", "src": "1470:33:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1462:4:1"}]}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_string_memory_ptr_t_string_memory_ptr__to_t_string_memory_ptr_t_string_memory_ptr_t_string_memory_ptr__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1133:9:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "1144:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "1152:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "1160:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1171:4:1", "type": ""}], "src": "963:546:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1643:119:1", "statements": [{"nodeType": "YulAssignment", "src": "1653:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1665:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1676:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1661:3:1"}, "nodeType": "YulFunctionCall", "src": "1661:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1653:4:1"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1695:9:1"}, {"name": "value0", "nodeType": "YulIdentifier", "src": "1706:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1688:6:1"}, "nodeType": "YulFunctionCall", "src": "1688:25:1"}, "nodeType": "YulExpressionStatement", "src": "1688:25:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1733:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1744:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1729:3:1"}, "nodeType": "YulFunctionCall", "src": "1729:18:1"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "1749:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1722:6:1"}, "nodeType": "YulFunctionCall", "src": "1722:34:1"}, "nodeType": "YulExpressionStatement", "src": "1722:34:1"}]}, "name": "abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1604:9:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "1615:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "1623:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1634:4:1", "type": ""}], "src": "1514:248:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1972:230:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1989:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2000:3:1", "type": "", "value": "128"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1982:6:1"}, "nodeType": "YulFunctionCall", "src": "1982:22:1"}, "nodeType": "YulExpressionStatement", "src": "1982:22:1"}, {"nodeType": "YulAssignment", "src": "2013:54:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "2039:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2051:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2062:3:1", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2047:3:1"}, "nodeType": "YulFunctionCall", "src": "2047:19:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "2021:17:1"}, "nodeType": "YulFunctionCall", "src": "2021:46:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2013:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2087:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2098:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2083:3:1"}, "nodeType": "YulFunctionCall", "src": "2083:18:1"}, {"name": "value1", "nodeType": "YulIdentifier", "src": "2103:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2076:6:1"}, "nodeType": "YulFunctionCall", "src": "2076:34:1"}, "nodeType": "YulExpressionStatement", "src": "2076:34:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2130:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2141:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2126:3:1"}, "nodeType": "YulFunctionCall", "src": "2126:18:1"}, {"name": "value2", "nodeType": "YulIdentifier", "src": "2146:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2119:6:1"}, "nodeType": "YulFunctionCall", "src": "2119:34:1"}, "nodeType": "YulExpressionStatement", "src": "2119:34:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2173:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2184:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2169:3:1"}, "nodeType": "YulFunctionCall", "src": "2169:18:1"}, {"name": "value3", "nodeType": "YulIdentifier", "src": "2189:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2162:6:1"}, "nodeType": "YulFunctionCall", "src": "2162:34:1"}, "nodeType": "YulExpressionStatement", "src": "2162:34:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_uint256_t_uint256_t_uint256__to_t_string_memory_ptr_t_uint256_t_uint256_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1917:9:1", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "1928:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "1936:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "1944:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "1952:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1963:4:1", "type": ""}], "src": "1767:435:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2302:92:1", "statements": [{"nodeType": "YulAssignment", "src": "2312:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2324:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2335:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2320:3:1"}, "nodeType": "YulFunctionCall", "src": "2320:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2312:4:1"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2354:9:1"}, {"arguments": [{"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "2379:6:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "2372:6:1"}, "nodeType": "YulFunctionCall", "src": "2372:14:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "2365:6:1"}, "nodeType": "YulFunctionCall", "src": "2365:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2347:6:1"}, "nodeType": "YulFunctionCall", "src": "2347:41:1"}, "nodeType": "YulExpressionStatement", "src": "2347:41:1"}]}, "name": "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2271:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "2282:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2293:4:1", "type": ""}], "src": "2207:187:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2500:76:1", "statements": [{"nodeType": "YulAssignment", "src": "2510:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2522:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2533:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2518:3:1"}, "nodeType": "YulFunctionCall", "src": "2518:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2510:4:1"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2552:9:1"}, {"name": "value0", "nodeType": "YulIdentifier", "src": "2563:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2545:6:1"}, "nodeType": "YulFunctionCall", "src": "2545:25:1"}, "nodeType": "YulExpressionStatement", "src": "2545:25:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2469:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "2480:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2491:4:1", "type": ""}], "src": "2399:177:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2630:176:1", "statements": [{"nodeType": "YulAssignment", "src": "2640:17:1", "value": {"arguments": [{"name": "x", "nodeType": "YulIdentifier", "src": "2652:1:1"}, {"name": "y", "nodeType": "YulIdentifier", "src": "2655:1:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "2648:3:1"}, "nodeType": "YulFunctionCall", "src": "2648:9:1"}, "variableNames": [{"name": "diff", "nodeType": "YulIdentifier", "src": "2640:4:1"}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2689:111:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2710:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2717:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2722:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "2713:3:1"}, "nodeType": "YulFunctionCall", "src": "2713:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2703:6:1"}, "nodeType": "YulFunctionCall", "src": "2703:31:1"}, "nodeType": "YulExpressionStatement", "src": "2703:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2754:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2757:4:1", "type": "", "value": "0x11"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2747:6:1"}, "nodeType": "YulFunctionCall", "src": "2747:15:1"}, "nodeType": "YulExpressionStatement", "src": "2747:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2782:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2785:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2775:6:1"}, "nodeType": "YulFunctionCall", "src": "2775:15:1"}, "nodeType": "YulExpressionStatement", "src": "2775:15:1"}]}, "condition": {"arguments": [{"name": "diff", "nodeType": "YulIdentifier", "src": "2672:4:1"}, {"name": "x", "nodeType": "YulIdentifier", "src": "2678:1:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "2669:2:1"}, "nodeType": "YulFunctionCall", "src": "2669:11:1"}, "nodeType": "YulIf", "src": "2666:134:1"}]}, "name": "checked_sub_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "x", "nodeType": "YulTypedName", "src": "2612:1:1", "type": ""}, {"name": "y", "nodeType": "YulTypedName", "src": "2615:1:1", "type": ""}], "returnVariables": [{"name": "diff", "nodeType": "YulTypedName", "src": "2621:4:1", "type": ""}], "src": "2581:225:1"}]}, "contents": "{\n    { }\n    function abi_encode_string(value, pos) -> end\n    {\n        let length := mload(value)\n        mstore(pos, length)\n        let i := 0\n        for { } lt(i, length) { i := add(i, 0x20) }\n        {\n            let _1 := 0x20\n            mstore(add(add(pos, i), _1), mload(add(add(value, i), _1)))\n        }\n        mstore(add(add(pos, length), 0x20), 0)\n        end := add(add(pos, and(add(length, 31), not(31))), 0x20)\n    }\n    function abi_encode_tuple_t_string_memory_ptr__to_t_string_memory_ptr__fromStack_reversed(headStart, value0) -> tail\n    {\n        mstore(headStart, 32)\n        tail := abi_encode_string(value0, add(headStart, 32))\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_uint256__fromStack_reversed(headStart, value1, value0) -> tail\n    {\n        mstore(headStart, 64)\n        tail := abi_encode_string(value0, add(headStart, 64))\n        mstore(add(headStart, 32), value1)\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_string_memory_ptr_t_string_memory_ptr__to_t_string_memory_ptr_t_string_memory_ptr_t_string_memory_ptr__fromStack_reversed(headStart, value2, value1, value0) -> tail\n    {\n        mstore(headStart, 96)\n        let tail_1 := abi_encode_string(value0, add(headStart, 96))\n        mstore(add(headStart, 32), sub(tail_1, headStart))\n        let tail_2 := abi_encode_string(value1, tail_1)\n        mstore(add(headStart, 64), sub(tail_2, headStart))\n        tail := abi_encode_string(value2, tail_2)\n    }\n    function abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed(headStart, value1, value0) -> tail\n    {\n        tail := add(headStart, 64)\n        mstore(headStart, value0)\n        mstore(add(headStart, 32), value1)\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_uint256_t_uint256_t_uint256__to_t_string_memory_ptr_t_uint256_t_uint256_t_uint256__fromStack_reversed(headStart, value3, value2, value1, value0) -> tail\n    {\n        mstore(headStart, 128)\n        tail := abi_encode_string(value0, add(headStart, 128))\n        mstore(add(headStart, 32), value1)\n        mstore(add(headStart, 64), value2)\n        mstore(add(headStart, 96), value3)\n    }\n    function abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, iszero(iszero(value0)))\n    }\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, value0)\n    }\n    function checked_sub_t_uint256(x, y) -> diff\n    {\n        diff := sub(x, y)\n        if gt(diff, x)\n        {\n            mstore(0, shl(224, 0x4e487b71))\n            mstore(4, 0x11)\n            revert(0, 0x24)\n        }\n    }\n}", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {"21": [{"length": 32, "start": 399}, {"length": 32, "start": 592}, {"length": 32, "start": 654}]}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x93 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x7A5B4F59 GT PUSH2 0x66 JUMPI DUP1 PUSH4 0x7A5B4F59 EQ PUSH2 0x12F JUMPI DUP1 PUSH4 0xAEEFFFAD EQ PUSH2 0x146 JUMPI DUP1 PUSH4 0xDABCB7DA EQ PUSH2 0x163 JUMPI DUP1 PUSH4 0xE4020804 EQ PUSH2 0x17B JUMPI DUP1 PUSH4 0xEAE4C19F EQ PUSH2 0x18A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x6FDDE03 EQ PUSH2 0x98 JUMPI DUP1 PUSH4 0x54FD4D50 EQ PUSH2 0xDD JUMPI DUP1 PUSH4 0x61461954 EQ PUSH2 0x101 JUMPI DUP1 PUSH4 0x7284E416 EQ PUSH2 0x127 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0xC7 PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x10 DUP2 MSTORE PUSH1 0x20 ADD PUSH16 0x1A19585C9D1899585D0B9AD95C9B995B PUSH1 0x82 SHL DUP2 MSTORE POP DUP2 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xD4 SWAP2 SWAP1 PUSH2 0x31F JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0xC7 PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5 DUP2 MSTORE PUSH1 0x20 ADD PUSH5 0x312E302E3 PUSH1 0xDC SHL DUP2 MSTORE POP DUP2 JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD DUP1 DUP3 ADD DUP3 MSTORE PUSH1 0x2 DUP2 MSTORE PUSH2 0x4F4B PUSH1 0xF0 SHL PUSH1 0x20 DUP3 ADD MSTORE SWAP1 MLOAD PUSH2 0xD4 SWAP2 SWAP1 TIMESTAMP SWAP1 PUSH2 0x339 JUMP JUMPDEST PUSH2 0xC7 PUSH2 0x1BF JUMP JUMPDEST PUSH2 0x137 PUSH2 0x1DB JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xD4 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x35B JUMP JUMPDEST PUSH2 0x14E PUSH2 0x24B JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD SWAP3 DUP4 MSTORE PUSH1 0x20 DUP4 ADD SWAP2 SWAP1 SWAP2 MSTORE ADD PUSH2 0xD4 JUMP JUMPDEST PUSH2 0x16B PUSH2 0x281 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0xD4 SWAP5 SWAP4 SWAP3 SWAP2 SWAP1 PUSH2 0x39E JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x1 DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0xD4 JUMP JUMPDEST PUSH2 0x1B1 PUSH32 0x0 DUP2 JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0xD4 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0x80 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5F DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x3F5 PUSH1 0x5F SWAP2 CODECOPY DUP2 JUMP JUMPDEST PUSH1 0x60 DUP1 PUSH1 0x60 PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x10 DUP2 MSTORE PUSH1 0x20 ADD PUSH16 0x1A19585C9D1899585D0B9AD95C9B995B PUSH1 0x82 SHL DUP2 MSTORE POP PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5 DUP2 MSTORE PUSH1 0x20 ADD PUSH5 0x312E302E3 PUSH1 0xDC SHL DUP2 MSTORE POP PUSH1 0x40 MLOAD DUP1 PUSH1 0x80 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5F DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x3F5 PUSH1 0x5F SWAP2 CODECOPY SWAP3 POP SWAP3 POP SWAP3 POP SWAP1 SWAP2 SWAP3 JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH32 0x0 PUSH2 0x279 DUP2 TIMESTAMP PUSH2 0x3CD JUMP JUMPDEST SWAP2 POP SWAP2 POP SWAP1 SWAP2 JUMP JUMPDEST PUSH1 0x60 PUSH1 0x0 DUP1 DUP1 TIMESTAMP NUMBER PUSH2 0x2B3 PUSH32 0x0 DUP4 PUSH2 0x3CD JUMP JUMPDEST PUSH1 0x40 DUP1 MLOAD DUP1 DUP3 ADD SWAP1 SWAP2 MSTORE PUSH1 0x2 DUP2 MSTORE PUSH2 0x4F4B PUSH1 0xF0 SHL PUSH1 0x20 DUP3 ADD MSTORE SWAP8 SWAP3 SWAP7 POP SWAP1 SWAP5 POP SWAP3 POP SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD DUP1 DUP5 MSTORE PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x2FF JUMPI PUSH1 0x20 DUP2 DUP6 ADD DUP2 ADD MLOAD DUP7 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0x2E3 JUMP JUMPDEST POP PUSH1 0x0 PUSH1 0x20 DUP3 DUP7 ADD ADD MSTORE PUSH1 0x20 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND DUP6 ADD ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x20 DUP2 MSTORE PUSH1 0x0 PUSH2 0x332 PUSH1 0x20 DUP4 ADD DUP5 PUSH2 0x2D9 JUMP JUMPDEST SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x40 DUP2 MSTORE PUSH1 0x0 PUSH2 0x34C PUSH1 0x40 DUP4 ADD DUP6 PUSH2 0x2D9 JUMP JUMPDEST SWAP1 POP DUP3 PUSH1 0x20 DUP4 ADD MSTORE SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x60 DUP2 MSTORE PUSH1 0x0 PUSH2 0x36E PUSH1 0x60 DUP4 ADD DUP7 PUSH2 0x2D9 JUMP JUMPDEST DUP3 DUP2 SUB PUSH1 0x20 DUP5 ADD MSTORE PUSH2 0x380 DUP2 DUP7 PUSH2 0x2D9 JUMP JUMPDEST SWAP1 POP DUP3 DUP2 SUB PUSH1 0x40 DUP5 ADD MSTORE PUSH2 0x394 DUP2 DUP6 PUSH2 0x2D9 JUMP JUMPDEST SWAP7 SWAP6 POP POP POP POP POP POP JUMP JUMPDEST PUSH1 0x80 DUP2 MSTORE PUSH1 0x0 PUSH2 0x3B1 PUSH1 0x80 DUP4 ADD DUP8 PUSH2 0x2D9 JUMP JUMPDEST PUSH1 0x20 DUP4 ADD SWAP6 SWAP1 SWAP6 MSTORE POP PUSH1 0x40 DUP2 ADD SWAP3 SWAP1 SWAP3 MSTORE PUSH1 0x60 SWAP1 SWAP2 ADD MSTORE SWAP2 SWAP1 POP JUMP JUMPDEST DUP2 DUP2 SUB DUP2 DUP2 GT ISZERO PUSH2 0x3EE JUMPI PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP3 SWAP2 POP POP JUMP INVALID COINBASE KECCAK256 PUSH14 0x696E696D616C206B65726E656C20 PUSH21 0x6861742072657475726E73206865616C7468207374 PUSH2 0x7475 PUSH20 0x20616E642063757272656E7420626C6F636B2074 PUSH10 0x6D657374616D7020666F PUSH19 0x206F6E2D636861696E206D6F6E69746F72696E PUSH8 0xA264697066735822 SLT KECCAK256 0xD 0x5C SWAP1 0xEB 0xF COINBASE PUSH22 0xCC7F968A98A871940184CCFCDABB978F386C34470A5F GT PUSH19 0x7C64736F6C6343000813003300000000000000 ", "sourceMap": "298:2680:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;479:48;;;;;;;;;;;;;;;-1:-1:-1;;;479:48:0;;;;;;;;;;;;:::i;:::-;;;;;;;;533:40;;;;;;;;;;;;;;;-1:-1:-1;;;533:40:0;;;;;1106:130;1199:30;;;;;;;;;;;-1:-1:-1;;;1199:30:0;;;;1106:130;;;;1199:30;1213:15;;1106:130;:::i;579:134::-;;;:::i;2123:222::-;;;:::i;:::-;;;;;;;;;:::i;2784:192::-;;;:::i;:::-;;;;1688:25:1;;;1744:2;1729:18;;1722:34;;;;1661:18;2784:192:0;1514:248:1;1569:321:0;;;:::i;:::-;;;;;;;;;;:::i;2512:86::-;;;2587:4;2347:41:1;;2335:2;2320:18;2512:86:0;2207:187:1;775:35:0;;;;;;;;2545:25:1;;;2533:2;2518:18;775:35:0;2399:177:1;579:134:0;;;;;;;;;;;;;;;;;;;:::o;2123:222::-;2178:26;2214:29;2253:33;2311:4;;;;;;;;;;;;;-1:-1:-1;;;2311:4:0;;;2317:7;;;;;;;;;;;;;-1:-1:-1;;;2317:7:0;;;2326:11;;;;;;;;;;;;;;;;;2303:35;;;;;;2123:222;;;:::o;2784:192::-;2845:27;;2928:10;2940:28;2928:10;2940:15;:28;:::i;:::-;2920:49;;;;2784:192;;:::o;1569:321::-;1628:20;1659:17;;;1788:15;1818:12;1845:28;1863:10;1788:15;1845:28;:::i;:::-;1748:135;;;;;;;;;;;;-1:-1:-1;;;1748:135:0;;;;;;;-1:-1:-1;1748:135:0;;-1:-1:-1;1748:135:0;-1:-1:-1;1569:321:0;-1:-1:-1;1569:321:0:o;14:423:1:-;56:3;94:5;88:12;121:6;116:3;109:19;146:1;156:162;170:6;167:1;164:13;156:162;;;232:4;288:13;;;284:22;;278:29;260:11;;;256:20;;249:59;185:12;156:162;;;160:3;363:1;356:4;347:6;342:3;338:16;334:27;327:38;426:4;419:2;415:7;410:2;402:6;398:15;394:29;389:3;385:39;381:50;374:57;;;14:423;;;;:::o;442:220::-;591:2;580:9;573:21;554:4;611:45;652:2;641:9;637:18;629:6;611:45;:::i;:::-;603:53;442:220;-1:-1:-1;;;442:220:1:o;667:291::-;844:2;833:9;826:21;807:4;864:45;905:2;894:9;890:18;882:6;864:45;:::i;:::-;856:53;;945:6;940:2;929:9;925:18;918:34;667:291;;;;;:::o;963:546::-;1208:2;1197:9;1190:21;1171:4;1234:45;1275:2;1264:9;1260:18;1252:6;1234:45;:::i;:::-;1327:9;1319:6;1315:22;1310:2;1299:9;1295:18;1288:50;1361:33;1387:6;1379;1361:33;:::i;:::-;1347:47;;1442:9;1434:6;1430:22;1425:2;1414:9;1410:18;1403:50;1470:33;1496:6;1488;1470:33;:::i;:::-;1462:41;963:546;-1:-1:-1;;;;;;963:546:1:o;1767:435::-;2000:3;1989:9;1982:22;1963:4;2021:46;2062:3;2051:9;2047:19;2039:6;2021:46;:::i;:::-;2098:2;2083:18;;2076:34;;;;-1:-1:-1;2141:2:1;2126:18;;2119:34;;;;2184:2;2169:18;;;2162:34;2013:54;1767:435;-1:-1:-1;1767:435:1:o;2581:225::-;2648:9;;;2669:11;;;2666:134;;;2722:10;2717:3;2713:20;2710:1;2703:31;2757:4;2754:1;2747:15;2785:4;2782:1;2775:15;2666:134;2581:225;;;;:::o"}, "methodIdentifiers": {"deployedAt()": "eae4c19f", "description()": "7284e416", "execute()": "61461954", "executeExtended()": "dabcb7da", "getDeploymentInfo()": "aeefffad", "getMetadata()": "7a5b4f59", "isHealthy()": "e4020804", "name()": "06fdde03", "version()": "54fd4d50"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.19+commit.7dd6d404\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"status\",\"type\":\"string\"}],\"name\":\"HeartbeatChecked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"deployedAt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"description\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"execute\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"status\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"executeExtended\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"status\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"uptime\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDeploymentInfo\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"deploymentTimestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"currentUptime\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMetadata\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"contractName\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"contractVersion\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"contractDescription\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isHealthy\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"healthy\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"A minimal kernel that returns health status and current block timestamp for on-chain monitoring\",\"kind\":\"dev\",\"methods\":{\"execute()\":{\"details\":\"Main heartbeat function that returns status and timestamp\",\"returns\":{\"status\":\"Always returns \\\"OK\\\" indicating the contract is operational\",\"timestamp\":\"Current block timestamp\"}},\"executeExtended()\":{\"details\":\"Extended heartbeat function with additional information\",\"returns\":{\"blockNumber\":\"Current block number\",\"status\":\"Always returns \\\"OK\\\" indicating the contract is operational\",\"timestamp\":\"Current block timestamp\",\"uptime\":\"Time elapsed since contract deployment\"}},\"getDeploymentInfo()\":{\"details\":\"Get deployment information\",\"returns\":{\"currentUptime\":\"Current uptime in seconds\",\"deploymentTimestamp\":\"When the contract was deployed\"}},\"getMetadata()\":{\"details\":\"Get contract metadata\",\"returns\":{\"contractDescription\":\"The description of the kernel\",\"contractName\":\"The name of the kernel\",\"contractVersion\":\"The version of the kernel\"}},\"isHealthy()\":{\"details\":\"Check if the contract is healthy (always returns true for this simple implementation)\",\"returns\":{\"healthy\":\"Always returns true\"}}},\"title\":\"HeartbeatKernel\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"This contract provides a simple heartbeat function that can be registered as a KRNL kernel\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/HeartbeatKernel.sol\":\"HeartbeatKernel\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/HeartbeatKernel.sol\":{\"keccak256\":\"0x5b3afde57fc5c7b137ee4b5337a2da8aef499840dae3819141db239901b6d828\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b806e04629071b2f47a7c5594c4c85429ea40293bc28c94695f1c061c660090\",\"dweb:/ipfs/QmNaWmY7vvT44eWkFeUxFWjDM3gbmgMXdWrwVU7y6P3uKr\"]}},\"version\":1}"}}}}}