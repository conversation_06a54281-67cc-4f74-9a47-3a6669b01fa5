{"_format": "hh-sol-cache-2", "files": {"/home/<USER>/Documents/GKM/KRNL/HeartBeat/heartbeat-kernel/contracts/HeartbeatKernel.sol": {"lastModificationDate": 1751827780882, "contentHash": "9cb757dadba69e49a33776d651c8ff47", "sourceName": "contracts/HeartbeatKernel.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.19"], "artifacts": ["HeartbeatKernel"]}}}