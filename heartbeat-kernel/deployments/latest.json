{"contractName": "HeartbeatKernel", "contractAddress": "0x5FbDB2315678afecb367f032d93F642f64180aa3", "deployerAddress": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "network": "hardhat", "chainId": "31337", "transactionHash": "0x6e3baf82c4e9f420e6378ddbf6d427ca1533b05b8b602aa0706910381ffbd68a", "blockNumber": 1, "gasUsed": "30000000", "timestamp": "2025-07-06T19:04:16.355Z", "abi": "[{\"type\":\"constructor\",\"stateMutability\":\"undefined\",\"payable\":false,\"inputs\":[]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"HeartbeatChecked\",\"inputs\":[{\"type\":\"address\",\"name\":\"caller\",\"indexed\":true},{\"type\":\"uint256\",\"name\":\"timestamp\",\"indexed\":false},{\"type\":\"string\",\"name\":\"status\",\"indexed\":false}]},{\"type\":\"function\",\"name\":\"deployedAt\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"uint256\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"description\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"execute\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"status\"},{\"type\":\"uint256\",\"name\":\"timestamp\"}]},{\"type\":\"function\",\"name\":\"executeExtended\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"status\"},{\"type\":\"uint256\",\"name\":\"timestamp\"},{\"type\":\"uint256\",\"name\":\"blockNumber\"},{\"type\":\"uint256\",\"name\":\"uptime\"}]},{\"type\":\"function\",\"name\":\"getDeploymentInfo\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"uint256\",\"name\":\"deploymentTimestamp\"},{\"type\":\"uint256\",\"name\":\"currentUptime\"}]},{\"type\":\"function\",\"name\":\"getMetadata\",\"constant\":true,\"stateMutability\":\"pure\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"contractName\"},{\"type\":\"string\",\"name\":\"contractVersion\"},{\"type\":\"string\",\"name\":\"contractDescription\"}]},{\"type\":\"function\",\"name\":\"isHealthy\",\"constant\":true,\"stateMutability\":\"pure\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"bool\",\"name\":\"healthy\"}]},{\"type\":\"function\",\"name\":\"name\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"version\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]}]", "bytecode": "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", "metadata": {"name": "heartbeat.kernel", "version": "1.0.0", "description": "A minimal kernel that returns health status and current block timestamp for on-chain monitoring"}}