{"name": "heartbeat.kernel", "version": "1.0.0", "address": "0x5FbDB2315678afecb367f032d93F642f64180aa3", "description": "A minimal kernel that returns health status and current block timestamp for on-chain monitoring", "network": "hardhat", "chainId": "31337", "abi": [{"type": "constructor", "stateMutability": "undefined", "payable": false, "inputs": []}, {"type": "event", "anonymous": false, "name": "HeartbeatChecked", "inputs": [{"type": "address", "name": "caller", "indexed": true}, {"type": "uint256", "name": "timestamp", "indexed": false}, {"type": "string", "name": "status", "indexed": false}]}, {"type": "function", "name": "deployedAt", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "description", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "execute", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": "status"}, {"type": "uint256", "name": "timestamp"}]}, {"type": "function", "name": "executeExtended", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": "status"}, {"type": "uint256", "name": "timestamp"}, {"type": "uint256", "name": "blockNumber"}, {"type": "uint256", "name": "uptime"}]}, {"type": "function", "name": "getDeploymentInfo", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "deploymentTimestamp"}, {"type": "uint256", "name": "currentUptime"}]}, {"type": "function", "name": "getMetadata", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": "contractName"}, {"type": "string", "name": "contractVersion"}, {"type": "string", "name": "contractDescription"}]}, {"type": "function", "name": "is<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "bool", "name": "healthy"}]}, {"type": "function", "name": "name", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "functions": {"execute": {"signature": "execute()", "returns": ["string", "uint256"], "description": "Returns status 'OK' and current block timestamp"}, "executeExtended": {"signature": "executeExtended()", "returns": ["string", "uint256", "uint256", "uint256"], "description": "Returns status, timestamp, block number, and uptime"}, "isHealthy": {"signature": "isHealthy()", "returns": ["bool"], "description": "Returns true if the contract is healthy"}}, "deploymentInfo": {"deployer": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "transactionHash": "0x6e3baf82c4e9f420e6378ddbf6d427ca1533b05b8b602aa0706910381ffbd68a", "blockNumber": 1, "timestamp": "2025-07-06T19:04:16.357Z"}}