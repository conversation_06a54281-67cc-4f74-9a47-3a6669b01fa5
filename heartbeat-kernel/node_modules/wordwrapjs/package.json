{"name": "wordwrapjs", "author": "<PERSON> <<EMAIL>>", "version": "4.0.1", "description": "Word-wrapping for javascript.", "repository": "https://github.com/75lb/wordwrapjs.git", "license": "MIT", "keywords": ["word", "line", "wrap", "text", "columns", "wordwrap"], "engines": {"node": ">=8.0.0"}, "scripts": {"test": "test-runner test.js", "docs": "jsdoc2md -t README.hbs index.js > README.md; echo"}, "devDependencies": {"jsdoc-to-markdown": "^7.0.0", "test-runner": "~0.6.3"}, "dependencies": {"reduce-flatten": "^2.0.0", "typical": "^5.2.0"}, "files": ["index.js"]}