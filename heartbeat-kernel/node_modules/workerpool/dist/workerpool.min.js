/*! For license information please see workerpool.min.js.LICENSE.txt */
!function(r,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("workerpool",[],e):"object"==typeof exports?exports.workerpool=e():r.workerpool=e()}("undefined"!=typeof self?self:this,(function(){return function(){var __webpack_modules__={345:function(r,e,t){var o=t(219),n=t(751),i=t(828),s=new(t(833));function u(r,e){"string"==typeof r?this.script=r||null:(this.script=null,e=r),this.workers=[],this.tasks=[],e=e||{},this.forkArgs=Object.freeze(e.forkArgs||[]),this.forkOpts=Object.freeze(e.forkOpts||{}),this.workerOpts=Object.freeze(e.workerOpts||{}),this.workerThreadOpts=Object.freeze(e.workerThreadOpts||{}),this.debugPortStart=e.debugPortStart||43210,this.nodeWorker=e.nodeWorker,this.workerType=e.workerType||e.nodeWorker||"auto",this.maxQueueSize=e.maxQueueSize||1/0,this.workerTerminateTimeout=e.workerTerminateTimeout||1e3,this.onCreateWorker=e.onCreateWorker||function(){return null},this.onTerminateWorker=e.onTerminateWorker||function(){return null},e&&"maxWorkers"in e?(function(r){if(!a(r)||!c(r)||r<1)throw new TypeError("Option maxWorkers must be an integer number >= 1")}(e.maxWorkers),this.maxWorkers=e.maxWorkers):this.maxWorkers=Math.max((i.cpus||4)-1,1),e&&"minWorkers"in e&&("max"===e.minWorkers?this.minWorkers=this.maxWorkers:(function(r){if(!a(r)||!c(r)||r<0)throw new TypeError("Option minWorkers must be an integer number >= 0")}(e.minWorkers),this.minWorkers=e.minWorkers,this.maxWorkers=Math.max(this.minWorkers,this.maxWorkers)),this._ensureMinWorkers()),this._boundNext=this._next.bind(this),"thread"===this.workerType&&n.ensureWorkerThreads()}function a(r){return"number"==typeof r}function c(r){return Math.round(r)==r}u.prototype.exec=function(r,e,t){if(e&&!Array.isArray(e))throw new TypeError('Array expected as argument "params"');if("string"==typeof r){var n=o.defer();if(this.tasks.length>=this.maxQueueSize)throw new Error("Max queue size of "+this.maxQueueSize+" reached");var i=this.tasks,s={method:r,params:e,resolver:n,timeout:null,options:t};i.push(s);var u=n.promise.timeout;return n.promise.timeout=function(r){return-1!==i.indexOf(s)?(s.timeout=r,n.promise):u.call(n.promise,r)},this._next(),n.promise}if("function"==typeof r)return this.exec("run",[String(r),e]);throw new TypeError('Function or string expected as argument "method"')},u.prototype.proxy=function(){if(arguments.length>0)throw new Error("No arguments expected");var r=this;return this.exec("methods").then((function(e){var t={};return e.forEach((function(e){t[e]=function(){return r.exec(e,Array.prototype.slice.call(arguments))}})),t}))},u.prototype._next=function(){if(this.tasks.length>0){var r=this._getWorker();if(r){var e=this,t=this.tasks.shift();if(t.resolver.promise.pending){var o=r.exec(t.method,t.params,t.resolver,t.options).then(e._boundNext).catch((function(){if(r.terminated)return e._removeWorker(r)})).then((function(){e._next()}));"number"==typeof t.timeout&&o.timeout(t.timeout)}else e._next()}}},u.prototype._getWorker=function(){for(var r=this.workers,e=0;e<r.length;e++){var t=r[e];if(!1===t.busy())return t}return r.length<this.maxWorkers?(t=this._createWorkerHandler(),r.push(t),t):null},u.prototype._removeWorker=function(r){var e=this;return s.releasePort(r.debugPort),this._removeWorkerFromList(r),this._ensureMinWorkers(),new o((function(t,o){r.terminate(!1,(function(n){e.onTerminateWorker({forkArgs:r.forkArgs,forkOpts:r.forkOpts,workerThreadOpts:r.workerThreadOpts,script:r.script}),n?o(n):t(r)}))}))},u.prototype._removeWorkerFromList=function(r){var e=this.workers.indexOf(r);-1!==e&&this.workers.splice(e,1)},u.prototype.terminate=function(r,e){var t=this;this.tasks.forEach((function(r){r.resolver.reject(new Error("Pool terminated"))})),this.tasks.length=0;var n=function(r){s.releasePort(r.debugPort),this._removeWorkerFromList(r)}.bind(this),i=[];return this.workers.slice().forEach((function(o){var s=o.terminateAndNotify(r,e).then(n).always((function(){t.onTerminateWorker({forkArgs:o.forkArgs,forkOpts:o.forkOpts,workerThreadOpts:o.workerThreadOpts,script:o.script})}));i.push(s)})),o.all(i)},u.prototype.stats=function(){var r=this.workers.length,e=this.workers.filter((function(r){return r.busy()})).length;return{totalWorkers:r,busyWorkers:e,idleWorkers:r-e,pendingTasks:this.tasks.length,activeTasks:e}},u.prototype._ensureMinWorkers=function(){if(this.minWorkers)for(var r=this.workers.length;r<this.minWorkers;r++)this.workers.push(this._createWorkerHandler())},u.prototype._createWorkerHandler=function(){var r=this.onCreateWorker({forkArgs:this.forkArgs,forkOpts:this.forkOpts,workerOpts:this.workerOpts,workerThreadOpts:this.workerThreadOpts,script:this.script})||{};return new n(r.script||this.script,{forkArgs:r.forkArgs||this.forkArgs,forkOpts:r.forkOpts||this.forkOpts,workerOpts:r.workerOpts||this.workerOpts,workerThreadOpts:r.workerThreadOpts||this.workerThreadOpts,debugPort:s.nextAvailableStartingAt(this.debugPortStart),workerType:this.workerType,workerTerminateTimeout:this.workerTerminateTimeout})},r.exports=u},219:function(r){"use strict";function e(r,i){var s=this;if(!(this instanceof e))throw new SyntaxError("Constructor must be called with the new operator");if("function"!=typeof r)throw new SyntaxError("Function parameter handler(resolve, reject) missing");var u=[],a=[];this.resolved=!1,this.rejected=!1,this.pending=!0;var c=function(r,e){u.push(r),a.push(e)};this.then=function(r,o){return new e((function(e,n){var i=r?t(r,e,n):e,s=o?t(o,e,n):n;c(i,s)}),s)};var f=function(r){return s.resolved=!0,s.rejected=!1,s.pending=!1,u.forEach((function(e){e(r)})),c=function(e,t){e(r)},f=p=function(){},s},p=function(r){return s.resolved=!1,s.rejected=!0,s.pending=!1,a.forEach((function(e){e(r)})),c=function(e,t){t(r)},f=p=function(){},s};this.cancel=function(){return i?i.cancel():p(new o),s},this.timeout=function(r){if(i)i.timeout(r);else{var e=setTimeout((function(){p(new n("Promise timed out after "+r+" ms"))}),r);s.always((function(){clearTimeout(e)}))}return s},r((function(r){f(r)}),(function(r){p(r)}))}function t(r,e,t){return function(o){try{var n=r(o);n&&"function"==typeof n.then&&"function"==typeof n.catch?n.then(e,t):e(n)}catch(r){t(r)}}}function o(r){this.message=r||"promise cancelled",this.stack=(new Error).stack}function n(r){this.message=r||"timeout exceeded",this.stack=(new Error).stack}e.prototype.catch=function(r){return this.then(null,r)},e.prototype.always=function(r){return this.then(r,r)},e.all=function(r){return new e((function(e,t){var o=r.length,n=[];o?r.forEach((function(r,i){r.then((function(r){n[i]=r,0==--o&&e(n)}),(function(r){o=0,t(r)}))})):e(n)}))},e.defer=function(){var r={};return r.promise=new e((function(e,t){r.resolve=e,r.reject=t})),r},o.prototype=new Error,o.prototype.constructor=Error,o.prototype.name="CancellationError",e.CancellationError=o,n.prototype=new Error,n.prototype.constructor=Error,n.prototype.name="TimeoutError",e.TimeoutError=n,r.exports=e},751:function(r,e,t){"use strict";function o(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,o=new Array(e);t<e;t++)o[t]=r[t];return o}function n(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),t.push.apply(t,o)}return t}function i(r){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},i(r)}var s=t(219),u=t(828),a=t(397),c="__workerpool-terminate__";function f(){var r=d();if(!r)throw new Error("WorkerPool: workerType = 'thread' is not supported, Node >= 11.7.0 required");return r}function p(){if("function"!=typeof Worker&&("object"!==("undefined"==typeof Worker?"undefined":i(Worker))||"function"!=typeof Worker.prototype.constructor))throw new Error("WorkerPool: Web Workers not supported")}function d(){try{return a("worker_threads")}catch(r){if("object"===i(r)&&null!==r&&"MODULE_NOT_FOUND"===r.code)return null;throw r}}function l(r,e,t){var o=new t(r,e);return o.isBrowserWorker=!0,o.on=function(r,e){this.addEventListener(r,(function(r){e(r.data)}))},o.send=function(r,e){this.postMessage(r,e)},o}function h(r,e,t){var o=new e.Worker(r,function(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?n(Object(t),!0).forEach((function(e){var o,n,s;o=r,n=e,s=t[e],(n=function(r){var e=function(r,e){if("object"!==i(r)||null===r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var o=t.call(r,"string");if("object"!==i(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}(r);return"symbol"===i(e)?e:String(e)}(n))in o?Object.defineProperty(o,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):o[n]=s})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))}))}return r}({stdout:!1,stderr:!1},t));return o.isWorkerThread=!0,o.send=function(r,e){this.postMessage(r,e)},o.kill=function(){return this.terminate(),!0},o.disconnect=function(){this.terminate()},o}function k(r,e,t){var o=t.fork(r,e.forkArgs,e.forkOpts),n=o.send;return o.send=function(r){return n.call(o,r)},o.isChildProcess=!0,o}function w(r){r=r||{};var e=process.execArgv.join(" "),t=-1!==e.indexOf("--inspect"),o=-1!==e.indexOf("--debug-brk"),n=[];return t&&(n.push("--inspect="+r.debugPort),o&&n.push("--debug-brk")),process.execArgv.forEach((function(r){r.indexOf("--max-old-space-size")>-1&&n.push(r)})),Object.assign({},r,{forkArgs:r.forkArgs,forkOpts:Object.assign({},r.forkOpts,{execArgv:(r.forkOpts&&r.forkOpts.execArgv||[]).concat(n)})})}function m(r,e){var n=this,i=e||{};function s(r){for(var e in n.terminated=!0,n.processing)void 0!==n.processing[e]&&n.processing[e].resolver.reject(r);n.processing=Object.create(null)}this.script=r||function(){if("browser"===u.platform){if("undefined"==typeof Blob)throw new Error("Blob not supported by the browser");if(!window.URL||"function"!=typeof window.URL.createObjectURL)throw new Error("URL.createObjectURL not supported by the browser");var r=new Blob([t(670)],{type:"text/javascript"});return window.URL.createObjectURL(r)}return __dirname+"/worker.js"}(),this.worker=function(r,e){if("web"===e.workerType)return p(),l(r,e.workerOpts,Worker);if("thread"===e.workerType)return h(r,t=f(),e.workerThreadOpts);if("process"!==e.workerType&&e.workerType){if("browser"===u.platform)return p(),l(r,e.workerOpts,Worker);var t=d();return t?h(r,t,e.workerThreadOpts):k(r,w(e),a("child_process"))}return k(r,w(e),a("child_process"))}(this.script,i),this.debugPort=i.debugPort,this.forkOpts=i.forkOpts,this.forkArgs=i.forkArgs,this.workerOpts=i.workerOpts,this.workerThreadOpts=i.workerThreadOpts,this.workerTerminateTimeout=i.workerTerminateTimeout,r||(this.worker.ready=!0),this.requestQueue=[],this.worker.on("message",(function(r){if(!n.terminated)if("string"==typeof r&&"ready"===r)n.worker.ready=!0,function(){var r,e=function(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=function(r,e){if(r){if("string"==typeof r)return o(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?o(r,e):void 0}}(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0,i=function(){};return{s:i,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,u=!0,a=!1;return{s:function(){t=t.call(r)},n:function(){var r=t.next();return u=r.done,r},e:function(r){a=!0,s=r},f:function(){try{u||null==t.return||t.return()}finally{if(a)throw s}}}}(n.requestQueue.splice(0));try{for(e.s();!(r=e.n()).done;){var t=r.value;n.worker.send(t.message,t.transfer)}}catch(r){e.e(r)}finally{e.f()}}();else{var e=r.id,t=n.processing[e];void 0!==t&&(r.isEvent?t.options&&"function"==typeof t.options.on&&t.options.on(r.payload):(delete n.processing[e],!0===n.terminating&&n.terminate(),r.error?t.resolver.reject(function(r){for(var e=new Error(""),t=Object.keys(r),o=0;o<t.length;o++)e[t[o]]=r[t[o]];return e}(r.error)):t.resolver.resolve(r.result)))}}));var c=this.worker;this.worker.on("error",s),this.worker.on("exit",(function(r,e){var t="Workerpool Worker terminated Unexpectedly\n";t+="    exitCode: `"+r+"`\n",t+="    signalCode: `"+e+"`\n",t+="    workerpool.script: `"+n.script+"`\n",t+="    spawnArgs: `"+c.spawnargs+"`\n",t+="    spawnfile: `"+c.spawnfile+"`\n",t+="    stdout: `"+c.stdout+"`\n",t+="    stderr: `"+c.stderr+"`\n",s(new Error(t))})),this.processing=Object.create(null),this.terminating=!1,this.terminated=!1,this.cleaning=!1,this.terminationHandler=null,this.lastId=0}m.prototype.methods=function(){return this.exec("methods")},m.prototype.exec=function(r,e,t,o){t||(t=s.defer());var n=++this.lastId;this.processing[n]={id:n,resolver:t,options:o};var i={message:{id:n,method:r,params:e},transfer:o&&o.transfer};this.terminated?t.reject(new Error("Worker is terminated")):this.worker.ready?this.worker.send(i.message,i.transfer):this.requestQueue.push(i);var u=this;return t.promise.catch((function(r){if(r instanceof s.CancellationError||r instanceof s.TimeoutError)return delete u.processing[n],u.terminateAndNotify(!0).then((function(){throw r}),(function(r){throw r}));throw r}))},m.prototype.busy=function(){return this.cleaning||Object.keys(this.processing).length>0},m.prototype.terminate=function(r,e){var t=this;if(r){for(var o in this.processing)void 0!==this.processing[o]&&this.processing[o].resolver.reject(new Error("Worker terminated"));this.processing=Object.create(null)}if("function"==typeof e&&(this.terminationHandler=e),this.busy())this.terminating=!0;else{var n=function(r){if(t.terminated=!0,t.cleaning=!1,null!=t.worker&&t.worker.removeAllListeners&&t.worker.removeAllListeners("message"),t.worker=null,t.terminating=!1,t.terminationHandler)t.terminationHandler(r,t);else if(r)throw r};if(this.worker){if("function"==typeof this.worker.kill){if(this.worker.killed)return void n(new Error("worker already killed!"));var i=setTimeout((function(){t.worker&&t.worker.kill()}),this.workerTerminateTimeout);return this.worker.once("exit",(function(){clearTimeout(i),t.worker&&(t.worker.killed=!0),n()})),this.worker.ready?this.worker.send(c):this.requestQueue.push({message:c}),void(this.cleaning=!0)}if("function"!=typeof this.worker.terminate)throw new Error("Failed to terminate worker");this.worker.terminate(),this.worker.killed=!0}n()}},m.prototype.terminateAndNotify=function(r,e){var t=s.defer();return e&&t.promise.timeout(e),this.terminate(r,(function(r,e){r?t.reject(r):t.resolve(e)})),t.promise},r.exports=m,r.exports._tryRequireWorkerThreads=d,r.exports._setupProcessWorker=k,r.exports._setupBrowserWorker=l,r.exports._setupWorkerThreadWorker=h,r.exports.ensureWorkerThreads=f},833:function(r){"use strict";function e(){this.ports=Object.create(null),this.length=0}r.exports=e,e.prototype.nextAvailableStartingAt=function(r){for(;!0===this.ports[r];)r++;if(r>=65535)throw new Error("WorkerPool debug port limit reached: "+r+">= 65535");return this.ports[r]=!0,this.length++,r},e.prototype.releasePort=function(r){delete this.ports[r],this.length--}},828:function(r,e,t){var o=t(397),n=function(r){return void 0!==r&&null!=r.versions&&null!=r.versions.node};r.exports.isNode=n,r.exports.platform="undefined"!=typeof process&&n(process)?"node":"browser";var i=function(r){try{return o("worker_threads")}catch(r){return null}}();r.exports.isMainThread="node"===r.exports.platform?(!i||i.isMainThread)&&!process.connected:"undefined"!=typeof Window,r.exports.cpus="browser"===r.exports.platform?self.navigator.hardwareConcurrency:o("os").cpus().length},670:function(r){r.exports='!function(){var __webpack_modules__={577:function(e){e.exports=function(e,r){this.message=e,this.transfer=r}}},__webpack_module_cache__={};function __webpack_require__(e){var r=__webpack_module_cache__[e];return void 0!==r||(r=__webpack_module_cache__[e]={exports:{}},__webpack_modules__[e](r,r.exports,__webpack_require__)),r.exports}var __webpack_exports__={};!function(){var exports=__webpack_exports__,__webpack_unused_export__;function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Transfer=__webpack_require__(577),requireFoolWebpack=eval("typeof require !== \'undefined\' ? require : function (module) { throw new Error(\'Module \\" + module + \\" not found.\') }"),TERMINATE_METHOD_ID="__workerpool-terminate__",worker={exit:function(){}},WorkerThreads,parentPort;if("undefined"!=typeof self&&"function"==typeof postMessage&&"function"==typeof addEventListener)worker.on=function(e,r){addEventListener(e,function(e){r(e.data)})},worker.send=function(e){postMessage(e)};else{if("undefined"==typeof process)throw new Error("Script must be executed as a worker");try{WorkerThreads=requireFoolWebpack("worker_threads")}catch(error){if("object"!==_typeof(error)||null===error||"MODULE_NOT_FOUND"!==error.code)throw error}WorkerThreads&&null!==WorkerThreads.parentPort?(parentPort=WorkerThreads.parentPort,worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort)):(worker.on=process.on.bind(process),worker.send=function(e){process.send(e)},worker.on("disconnect",function(){process.exit(1)})),worker.exit=process.exit.bind(process)}function convertError(o){return Object.getOwnPropertyNames(o).reduce(function(e,r){return Object.defineProperty(e,r,{value:o[r],enumerable:!0})},{})}function isPromise(e){return e&&"function"==typeof e.then&&"function"==typeof e.catch}worker.methods={},worker.methods.run=function(e,r){e=new Function("return ("+e+").apply(null, arguments);");return e.apply(e,r)},worker.methods.methods=function(){return Object.keys(worker.methods)},worker.terminationHandler=void 0,worker.cleanupAndExit=function(e){function r(){worker.exit(e)}if(!worker.terminationHandler)return r();var o=worker.terminationHandler(e);isPromise(o)?o.then(r,r):r()};var currentRequestId=null;worker.on("message",function(r){if(r===TERMINATE_METHOD_ID)return worker.cleanupAndExit(0);try{var e=worker.methods[r.method];if(!e)throw new Error(\'Unknown method "\'+r.method+\'"\');currentRequestId=r.id;var o=e.apply(e,r.params);isPromise(o)?o.then(function(e){e instanceof Transfer?worker.send({id:r.id,result:e.message,error:null},e.transfer):worker.send({id:r.id,result:e,error:null}),currentRequestId=null}).catch(function(e){worker.send({id:r.id,result:null,error:convertError(e)}),currentRequestId=null}):(o instanceof Transfer?worker.send({id:r.id,result:o.message,error:null},o.transfer):worker.send({id:r.id,result:o,error:null}),currentRequestId=null)}catch(e){worker.send({id:r.id,result:null,error:convertError(e)})}}),worker.register=function(e,r){if(e)for(var o in e)e.hasOwnProperty(o)&&(worker.methods[o]=e[o]);r&&(worker.terminationHandler=r.onTerminate),worker.send("ready")},worker.emit=function(e){currentRequestId&&(e instanceof Transfer?worker.send({id:currentRequestId,isEvent:!0,payload:e.message},e.transfer):worker.send({id:currentRequestId,isEvent:!0,payload:e}))},__webpack_unused_export__=worker.register,worker.emit}()}();'},397:function(module){var requireFoolWebpack=eval("typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \" + module + \" not found.') }");module.exports=requireFoolWebpack},577:function(r){r.exports=function(r,e){this.message=r,this.transfer=e}},744:function(__unused_webpack_module,exports,__webpack_require__){function _typeof(r){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof(r)}var Transfer=__webpack_require__(577),requireFoolWebpack=eval("typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \" + module + \" not found.') }"),TERMINATE_METHOD_ID="__workerpool-terminate__",worker={exit:function(){}};if("undefined"!=typeof self&&"function"==typeof postMessage&&"function"==typeof addEventListener)worker.on=function(r,e){addEventListener(r,(function(r){e(r.data)}))},worker.send=function(r){postMessage(r)};else{if("undefined"==typeof process)throw new Error("Script must be executed as a worker");var WorkerThreads;try{WorkerThreads=requireFoolWebpack("worker_threads")}catch(r){if("object"!==_typeof(r)||null===r||"MODULE_NOT_FOUND"!==r.code)throw r}if(WorkerThreads&&null!==WorkerThreads.parentPort){var parentPort=WorkerThreads.parentPort;worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort),worker.exit=process.exit.bind(process)}else worker.on=process.on.bind(process),worker.send=function(r){process.send(r)},worker.on("disconnect",(function(){process.exit(1)})),worker.exit=process.exit.bind(process)}function convertError(r){return Object.getOwnPropertyNames(r).reduce((function(e,t){return Object.defineProperty(e,t,{value:r[t],enumerable:!0})}),{})}function isPromise(r){return r&&"function"==typeof r.then&&"function"==typeof r.catch}worker.methods={},worker.methods.run=function(r,e){var t=new Function("return ("+r+").apply(null, arguments);");return t.apply(t,e)},worker.methods.methods=function(){return Object.keys(worker.methods)},worker.terminationHandler=void 0,worker.cleanupAndExit=function(r){var e=function(){worker.exit(r)};if(!worker.terminationHandler)return e();var t=worker.terminationHandler(r);isPromise(t)?t.then(e,e):e()};var currentRequestId=null;worker.on("message",(function(r){if(r===TERMINATE_METHOD_ID)return worker.cleanupAndExit(0);try{var e=worker.methods[r.method];if(!e)throw new Error('Unknown method "'+r.method+'"');currentRequestId=r.id;var t=e.apply(e,r.params);isPromise(t)?t.then((function(e){e instanceof Transfer?worker.send({id:r.id,result:e.message,error:null},e.transfer):worker.send({id:r.id,result:e,error:null}),currentRequestId=null})).catch((function(e){worker.send({id:r.id,result:null,error:convertError(e)}),currentRequestId=null})):(t instanceof Transfer?worker.send({id:r.id,result:t.message,error:null},t.transfer):worker.send({id:r.id,result:t,error:null}),currentRequestId=null)}catch(e){worker.send({id:r.id,result:null,error:convertError(e)})}})),worker.register=function(r,e){if(r)for(var t in r)r.hasOwnProperty(t)&&(worker.methods[t]=r[t]);e&&(worker.terminationHandler=e.onTerminate),worker.send("ready")},worker.emit=function(r){if(currentRequestId){if(r instanceof Transfer)return void worker.send({id:currentRequestId,isEvent:!0,payload:r.message},r.transfer);worker.send({id:currentRequestId,isEvent:!0,payload:r})}},exports.add=worker.register,exports.emit=worker.emit}},__webpack_module_cache__={};function __webpack_require__(r){var e=__webpack_module_cache__[r];if(void 0!==e)return e.exports;var t=__webpack_module_cache__[r]={exports:{}};return __webpack_modules__[r](t,t.exports,__webpack_require__),t.exports}var __webpack_exports__={};return function(){var r=__webpack_exports__,e=__webpack_require__(828);r.pool=function(r,e){return new(__webpack_require__(345))(r,e)},r.worker=function(r,e){__webpack_require__(744).add(r,e)},r.workerEmit=function(r){__webpack_require__(744).emit(r)},r.Promise=__webpack_require__(219),r.Transfer=__webpack_require__(577),r.platform=e.platform,r.isMainThread=e.isMainThread,r.cpus=e.cpus}(),__webpack_exports__}()}));
//# sourceMappingURL=workerpool.min.js.map