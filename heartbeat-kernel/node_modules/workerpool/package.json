{"name": "workerpool", "license": "Apache-2.0", "version": "6.5.1", "description": "Offload tasks to a pool of workers on node.js and in the browser", "homepage": "https://github.com/josdejong/workerpool", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/josdejong)", "repository": {"type": "git", "url": "git://github.com/josdejong/workerpool.git"}, "keywords": ["worker", "web worker", "cluster", "pool", "isomorphic"], "main": "src/index.js", "browser": "dist/workerpool.js", "files": ["dist", "src", "HISTORY.md", "LICENSE", "README.md"], "scripts": {"build": "gulp", "watch": "gulp watch", "test": "npm run build && mocha test", "test:debug": "npm run build && mocha debug test", "coverage": "npm run build && c8 mocha && c8 report --reporter=html && echo Coverage report is available at ./coverage/index.html", "prepublishOnly": "npm run test"}, "devDependencies": {"@babel/core": "7.23.0", "@babel/preset-env": "7.22.20", "babel-loader": "9.1.3", "c8": "8.0.1", "date-format": "4.0.14", "del": "6.1.1", "fancy-log": "2.0.0", "find-process": "1.4.7", "gulp": "4.0.2", "handlebars": "4.7.8", "mocha": "10.2.0", "uglify-js": "3.17.4", "webpack": "5.88.2"}}