/**
 * embeddedWorker.js contains an embedded version of worker.js.
 * This file is automatically generated,
 * changes made in this file will be overwritten.
 */
module.exports = "!function(){var __webpack_modules__={577:function(e){e.exports=function(e,r){this.message=e,this.transfer=r}}},__webpack_module_cache__={};function __webpack_require__(e){var r=__webpack_module_cache__[e];return void 0!==r||(r=__webpack_module_cache__[e]={exports:{}},__webpack_modules__[e](r,r.exports,__webpack_require__)),r.exports}var __webpack_exports__={};!function(){var exports=__webpack_exports__,__webpack_unused_export__;function _typeof(e){return(_typeof=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}var Transfer=__webpack_require__(577),requireFoolWebpack=eval(\"typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \\\" + module + \\\" not found.') }\"),TERMINATE_METHOD_ID=\"__workerpool-terminate__\",worker={exit:function(){}},WorkerThreads,parentPort;if(\"undefined\"!=typeof self&&\"function\"==typeof postMessage&&\"function\"==typeof addEventListener)worker.on=function(e,r){addEventListener(e,function(e){r(e.data)})},worker.send=function(e){postMessage(e)};else{if(\"undefined\"==typeof process)throw new Error(\"Script must be executed as a worker\");try{WorkerThreads=requireFoolWebpack(\"worker_threads\")}catch(error){if(\"object\"!==_typeof(error)||null===error||\"MODULE_NOT_FOUND\"!==error.code)throw error}WorkerThreads&&null!==WorkerThreads.parentPort?(parentPort=WorkerThreads.parentPort,worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort)):(worker.on=process.on.bind(process),worker.send=function(e){process.send(e)},worker.on(\"disconnect\",function(){process.exit(1)})),worker.exit=process.exit.bind(process)}function convertError(o){return Object.getOwnPropertyNames(o).reduce(function(e,r){return Object.defineProperty(e,r,{value:o[r],enumerable:!0})},{})}function isPromise(e){return e&&\"function\"==typeof e.then&&\"function\"==typeof e.catch}worker.methods={},worker.methods.run=function(e,r){e=new Function(\"return (\"+e+\").apply(null, arguments);\");return e.apply(e,r)},worker.methods.methods=function(){return Object.keys(worker.methods)},worker.terminationHandler=void 0,worker.cleanupAndExit=function(e){function r(){worker.exit(e)}if(!worker.terminationHandler)return r();var o=worker.terminationHandler(e);isPromise(o)?o.then(r,r):r()};var currentRequestId=null;worker.on(\"message\",function(r){if(r===TERMINATE_METHOD_ID)return worker.cleanupAndExit(0);try{var e=worker.methods[r.method];if(!e)throw new Error('Unknown method \"'+r.method+'\"');currentRequestId=r.id;var o=e.apply(e,r.params);isPromise(o)?o.then(function(e){e instanceof Transfer?worker.send({id:r.id,result:e.message,error:null},e.transfer):worker.send({id:r.id,result:e,error:null}),currentRequestId=null}).catch(function(e){worker.send({id:r.id,result:null,error:convertError(e)}),currentRequestId=null}):(o instanceof Transfer?worker.send({id:r.id,result:o.message,error:null},o.transfer):worker.send({id:r.id,result:o,error:null}),currentRequestId=null)}catch(e){worker.send({id:r.id,result:null,error:convertError(e)})}}),worker.register=function(e,r){if(e)for(var o in e)e.hasOwnProperty(o)&&(worker.methods[o]=e[o]);r&&(worker.terminationHandler=r.onTerminate),worker.send(\"ready\")},worker.emit=function(e){currentRequestId&&(e instanceof Transfer?worker.send({id:currentRequestId,isEvent:!0,payload:e.message},e.transfer):worker.send({id:currentRequestId,isEvent:!0,payload:e}))},__webpack_unused_export__=worker.register,worker.emit}()}();";
